#!/usr/bin/env python3
"""
Verify the epg_tv_guide package structure matches system expectations
"""

import os
import sys

def verify_structure():
    """Verify the package structure"""
    print("🔍 Verifying epg_tv_guide package structure...")
    
    # Check if package directory exists
    if not os.path.exists("epg_tv_guide"):
        print("❌ epg_tv_guide directory not found")
        return False
    
    print("✅ epg_tv_guide directory exists")
    
    # Check if __init__.py exists
    if not os.path.exists("epg_tv_guide/__init__.py"):
        print("❌ epg_tv_guide/__init__.py not found")
        return False
    
    print("✅ epg_tv_guide/__init__.py exists")
    
    # Check if agent.py exists (for epg_tv_guide.agent.root_agent path)
    if not os.path.exists("epg_tv_guide/agent.py"):
        print("❌ epg_tv_guide/agent.py not found")
        return False
    
    print("✅ epg_tv_guide/agent.py exists")
    
    # Check if .env file exists
    if not os.path.exists(".env"):
        print("❌ .env file not found")
        return False
    
    print("✅ .env file exists")
    
    return True

def verify_imports():
    """Verify all expected import paths work"""
    print("\n🔍 Verifying import paths...")
    
    # Test 1: epg_tv_guide.root_agent
    try:
        from epg_tv_guide import root_agent
        print("✅ from epg_tv_guide import root_agent - SUCCESS")
    except Exception as e:
        print(f"❌ from epg_tv_guide import root_agent - FAILED: {e}")
        return False
    
    # Test 2: epg_tv_guide.agent.root_agent  
    try:
        from epg_tv_guide.agent import root_agent as agent_root
        print("✅ from epg_tv_guide.agent import root_agent - SUCCESS")
    except Exception as e:
        print(f"❌ from epg_tv_guide.agent import root_agent - FAILED: {e}")
        return False
    
    # Test 3: Verify they're the same
    if root_agent is agent_root:
        print("✅ Both import paths reference the same object")
    else:
        print("⚠️  Import paths reference different objects")
    
    return True

def verify_functionality():
    """Verify the root_agent functionality"""
    print("\n🔍 Verifying root_agent functionality...")
    
    try:
        from epg_tv_guide import root_agent
        
        # Check if it has the expected query method
        if hasattr(root_agent, 'query'):
            print("✅ root_agent has query method")
        else:
            print("❌ root_agent missing query method")
            return False
        
        # Test the query method (this may fail if Neo4j is not available)
        try:
            result = root_agent.query("test")
            if isinstance(result, dict):
                print("✅ root_agent.query() returns dict")
                if 'status' in result:
                    print(f"✅ Query status: {result['status']}")
                else:
                    print("⚠️  Query result missing 'status' field")
            else:
                print(f"⚠️  root_agent.query() returns {type(result)}, expected dict")
        except Exception as e:
            print(f"⚠️  root_agent.query() failed: {e}")
            print("   This may be expected if Neo4j is not available")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing root_agent functionality: {e}")
        return False

def main():
    """Run all verification tests"""
    print("🧪 EPG TV Guide Package Structure Verification")
    print("=" * 60)
    
    structure_ok = verify_structure()
    imports_ok = verify_imports()
    functionality_ok = verify_functionality()
    
    print("\n" + "=" * 60)
    print("📊 Verification Summary:")
    print(f"   Structure: {'✅ PASS' if structure_ok else '❌ FAIL'}")
    print(f"   Imports: {'✅ PASS' if imports_ok else '❌ FAIL'}")
    print(f"   Functionality: {'✅ PASS' if functionality_ok else '❌ FAIL'}")
    
    if all([structure_ok, imports_ok, functionality_ok]):
        print("\n🎉 All verifications passed!")
        print("   The package structure meets system expectations.")
        return True
    else:
        print("\n❌ Some verifications failed!")
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
