#!/usr/bin/env python3
"""
Test the orchestration function to ensure it provides complete workflow execution
"""

def test_orchestration_function():
    """Test the orchestrate_complete_workflow function"""
    print("🔍 Testing Orchestration Function...")
    print("=" * 50)
    
    try:
        from epg_tv_guide.agents.agent import orchestrate_complete_workflow
        
        # Test Formula One query
        question = "When is the Formula One program airing?"
        print(f"Testing question: {question}")
        
        result = orchestrate_complete_workflow(question)
        
        print(f"Status: {result.get('status', 'unknown')}")
        print(f"Workflow Completed: {result.get('workflow_completed', False)}")
        print(f"Answer: {result.get('answer', 'No answer')}")
        print(f"Orchestration Note: {result.get('orchestration_note', 'No note')}")
        
        if result.get('workflow_completed'):
            print("✅ Orchestration function completed workflow")
        else:
            print("⚠️  Orchestration function did not complete workflow")
        
        # Check if it returns actual data instead of validation patterns
        answer = result.get('answer', '')
        if 'VALIDATED:' in answer:
            print("❌ Still returning validation patterns")
            return False
        elif 'Neo4j connection not available' in answer:
            print("✅ Expected error (Neo4j not connected) - orchestration working")
            return True
        else:
            print("✅ Returning actual data")
            return True
        
    except Exception as e:
        print(f"❌ Error testing orchestration: {e}")
        return False

def test_agent_has_orchestration_tool():
    """Test that the agent has the orchestration tool"""
    print("\n🔍 Testing Agent Tool Configuration...")
    print("=" * 50)
    
    try:
        from epg_tv_guide.agents.agent import super_sport_agent
        
        print(f"Agent name: {super_sport_agent.name}")
        print(f"Number of tools: {len(super_sport_agent.tools)}")
        
        if hasattr(super_sport_agent, 'tools') and super_sport_agent.tools:
            tool_names = []
            for tool in super_sport_agent.tools:
                if hasattr(tool, '__name__'):
                    tool_names.append(tool.__name__)
                else:
                    tool_names.append(str(tool))
            
            print(f"Tool names: {tool_names}")
            
            if 'orchestrate_complete_workflow' in tool_names:
                print("✅ Agent has orchestration tool")
                return True
            else:
                print("❌ Agent missing orchestration tool")
                return False
        else:
            print("❌ Agent has no tools")
            return False
        
    except Exception as e:
        print(f"❌ Error checking agent tools: {e}")
        return False

def test_agent_instructions():
    """Test that agent instructions mention orchestration"""
    print("\n🔍 Testing Agent Instructions...")
    print("=" * 50)
    
    try:
        from epg_tv_guide.agents.agent import super_sport_agent
        
        instructions = super_sport_agent.instruction
        
        orchestration_keywords = [
            "orchestrate_complete_workflow",
            "IMMEDIATELY call orchestrate_complete_workflow",
            "MANDATORY WORKFLOW EXECUTION",
            "NEVER attempt manual sub-agent coordination"
        ]
        
        print("Checking for orchestration keywords:")
        found_count = 0
        for keyword in orchestration_keywords:
            if keyword.lower() in instructions.lower():
                print(f"   ✅ Found: '{keyword}'")
                found_count += 1
            else:
                print(f"   ❌ Missing: '{keyword}'")
        
        return found_count >= 3  # At least 3 out of 4 keywords should be present
        
    except Exception as e:
        print(f"❌ Error checking instructions: {e}")
        return False

def main():
    """Run all orchestration tests"""
    print("Orchestration Function Test Suite")
    print("=" * 60)
    print("Testing the orchestration function to ensure complete workflow execution")
    
    success = True
    
    # Test 1: Orchestration function
    success &= test_orchestration_function()
    
    # Test 2: Agent tool configuration
    success &= test_agent_has_orchestration_tool()
    
    # Test 3: Agent instructions
    success &= test_agent_instructions()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 All orchestration tests passed!")
        print("\nKey Features:")
        print("✅ Orchestration function executes complete workflow")
        print("✅ Agent has orchestration tool available")
        print("✅ Agent instructions prioritize orchestration")
        print("\nExpected Behavior:")
        print("User: 'When is Formula One airing?'")
        print("Agent: [Calls orchestrate_complete_workflow] → [Returns Final Results]")
    else:
        print("💥 Some orchestration tests failed.")
        print("The agent may still stop at validation patterns.")

if __name__ == "__main__":
    main()
