#!/usr/bin/env python3
"""
Test root_agent imports and functionality
"""

import sys

def test_complete_setup():
    """Test the complete epg_tv_guide setup"""
    print('Testing complete epg_tv_guide setup...')

    # Test 1: Package import
    try:
        import epg_tv_guide
        print('✅ Package import successful')
    except Exception as e:
        print(f'❌ Package import failed: {e}')
        return False

    # Test 2: Root agent import from package
    try:
        from epg_tv_guide import root_agent
        print('✅ root_agent import from package successful')
    except Exception as e:
        print(f'❌ root_agent import from package failed: {e}')
        return False

    # Test 3: Root agent import from agent module
    try:
        from epg_tv_guide.agent import root_agent as agent_root
        print('✅ root_agent import from agent module successful')
    except Exception as e:
        print(f'❌ root_agent import from agent module failed: {e}')
        return False

    # Test 4: Verify they are the same object
    if root_agent is agent_root:
        print('✅ Both root_agent imports reference the same object')
    else:
        print('⚠️  root_agent imports reference different objects')

    # Test 5: Test agent functionality
    try:
        result = root_agent.query('test query')
        print('✅ root_agent.query() method works')
        print(f'   Result type: {type(result)}')
        if isinstance(result, dict) and 'status' in result:
            print(f'   Status: {result.get("status", "unknown")}')
    except Exception as e:
        print(f'❌ root_agent.query() failed: {e}')
        print('   This may be expected if Neo4j is not available')

    print('🎉 All tests completed!')
    return True

if __name__ == "__main__":
    success = test_complete_setup()
    if not success:
        sys.exit(1)
