#!/usr/bin/env python3
"""
Test the single agent implementation
"""

def test_single_agent_import():
    """Test that the single agent can be imported and used"""
    print("🔍 Testing single agent import...")
    
    try:
        from epg_tv_guide import super_sport_agent
        
        print("✅ Successfully imported super_sport_agent")
        print(f"   Agent type: {type(super_sport_agent).__name__}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error importing agent: {e}")
        return False

def test_backward_compatibility():
    """Test that old agent imports still work with deprecation warnings"""
    print("\n🔄 Testing backward compatibility...")
    
    try:
        from epg_tv_guide import relationship_validator_agent, query_executor_agent, execute_final_query
        
        print("✅ Successfully imported deprecated agents")
        print(f"   Validator agent: {relationship_validator_agent.name}")
        print(f"   Executor agent: {query_executor_agent.name}")
        print(f"   Execute function: {execute_final_query.__name__}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error importing deprecated agents: {e}")
        return False

def test_agent_functionality():
    """Test basic agent functionality"""
    print("\n🎯 Testing agent functionality...")
    
    try:
        from epg_tv_guide import super_sport_agent
        
        # Test with a simple question
        test_question = "What programs are available?"
        print(f"   Testing question: '{test_question}'")
        
        # Note: This will fail if Neo4j is not available, but we can test the structure
        result = super_sport_agent.query(test_question)
        
        print("✅ Agent query method executed")
        print(f"   Result type: {type(result)}")
        print(f"   Result keys: {list(result.keys()) if isinstance(result, dict) else 'Not a dict'}")
        
        # Check expected structure
        if isinstance(result, dict):
            expected_keys = ['status', 'question', 'answer', 'agent']
            has_expected_structure = all(key in result for key in expected_keys)
            
            if has_expected_structure:
                print("✅ Result has expected structure")
            else:
                print("⚠️  Result structure may be different than expected")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing agent functionality: {e}")
        print(f"   This is expected if Neo4j is not available")
        return False

def main():
    """Run all tests"""
    print("🧪 Testing Single Agent Implementation")
    print("=" * 50)
    
    tests = [
        test_single_agent_import,
        test_backward_compatibility,
        test_agent_functionality
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}")
            results.append(False)
    
    print("\n" + "=" * 50)
    print("📊 Test Summary:")
    print(f"   Passed: {sum(results)}/{len(results)}")
    
    if all(results):
        print("🎉 All tests passed!")
    else:
        print("⚠️  Some tests failed (may be expected if Neo4j not available)")
    
    print("\n✨ Single agent refactoring complete!")
    print("   - Complex multi-agent architecture → Simple single agent")
    print("   - Backward compatibility maintained")
    print("   - Direct knowledge graph integration")

if __name__ == "__main__":
    main()
